// uno.config.js
import { defineConfig, presetUno, presetIcons } from "unocss";
import { presetWind3 } from "@unocss/preset-wind3";
export default defineConfig({
  presets: [
    presetWind3(), // 基础预设
    presetIcons({
      scale: 1.2,
      warn: true,
    }), // 图标预设
  ],
  shortcuts: {
    // 布局相关
    "layout-container": "h-screen w-full flex",
    sidebar: "w-60 h-full bg-gray-900 text-white overflow-y-auto",
    "main-container": "flex-1 flex flex-col overflow-hidden",
    header: "h-14 bg-white border-b border-gray-200 flex-between px-4",
    "content-container": "flex-1 p-4 overflow-auto bg-gray-100",
    "right-panel": "w-80 border-l border-gray-200 bg-white p-4 overflow-y-auto",

    // 导航相关
    "nav-item": "flex items-center px-4 py-3 cursor-pointer hover:bg-gray-800",
    "nav-item-active": "bg-blue-600 hover:bg-blue-700",
    "step-item": "flex items-center px-4 py-2 cursor-pointer",

    // 内容区域
    card: "bg-white rounded-md shadow-sm p-4",
    "form-item": "mb-4",
  },
  theme: {
    colors: {
      primary: "#3370ff",
      success: "#52c41a",
      warning: "#faad14",
      danger: "#ff4d4f",
      info: "#909399",
    },
  },
});
