import { createRouter, createWebHistory } from 'vue-router';

// 定义路由
const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue'),
    meta: { title: '首页' },
  },
  {
    path: '/course-generator',
    name: 'CourseGenerator',
    component: () => import('../views/CourseGenerator.vue'),
    meta: { title: '交互式视频课程生成器' },
  },
  {
    path: '/video-gallery',
    name: 'VideoGallery',
    component: () => import('../views/VideoGallery.vue'),
    meta: { title: '视频展示' },
  },
];

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
});

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title || '火山引擎管理系统';
  next();
});

export default router;
