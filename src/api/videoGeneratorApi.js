import { get, post, put } from './http';

// 视频生成器相关API
export const videoGeneratorApi = {
  // 生成场景大纲
  generateSceneOutline: (data) => {
    return post('/generateSceneOutline', data);
  },

  // 保存场景大纲
  saveSceneOutline: (data) => {
    return put('/video/scene-outline', data);
  },

  // 生成实现计划
  generateStoryboard: (data) => {
    return post('/generateStoryboard', data);
  },
  // 生成技术实现
  generateTechnicalPlan: (data) => {
    return post('/generateTechnicalImplementation', data);
  },
  // 生成动画旁白
  generateAnimationNarration: (data) => {
    return post('/generateAnimationNarration', data);
  },
  // 生成视频
  generateVideo: (data) => {
    return post('/generateVideo', data);
  },
  // 视频合并
  generateVideoMerge(data) {
    return post('/generateVideoMerge', data);
  },

  // 获取视频生成进度
  getVideoProgress: (id) => {
    return get(`/video/progress/${id}`);
  },

  // 获取历史记录
  getHistory: () => {
    return get('/video/history');
  },

  // 获取单个视频详情
  getVideoDetail: (id) => {
    return get(`/video/detail/${id}`);
  },
};
