import axios from 'axios';

// 创建 axios 实例
const http = axios.create({
  baseURL: 'http://localhost:3001/frontend-chat', // 设置基础URL
  // timeout: 30000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    // 在发送请求之前做些什么
    // 例如：添加token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    // 对请求错误做些什么
    return Promise.reject(error);
  }
);

// 响应拦截器
http.interceptors.response.use(
  (response) => {
    // 对响应数据做点什么
    return response.data;
  },
  (error) => {
    // 对响应错误做点什么
    if (error.response) {
      // 服务器返回了错误状态码
      switch (error.response.status) {
        case 401:
          // 未授权，可能需要登录
          console.error('未授权，请登录');
          break;
        case 403:
          // 禁止访问
          console.error('没有权限访问');
          break;
        case 404:
          // 资源不存在
          console.error('请求的资源不存在');
          break;
        case 500:
          // 服务器错误
          console.error('服务器错误');
          break;
        default:
          console.error(`请求错误: ${error.response.status}`);
      }
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      console.error('网络错误，无法连接到服务器');
    } else {
      // 请求配置有误
      console.error('请求配置错误:', error.message);
    }
    return Promise.reject(error);
  }
);

// 封装GET请求
export const get = (url, params = {}) => {
  return http.get(url, { params });
};

// 封装POST请求
export const post = (url, data = {}) => {
  return http.post(url, data);
};

// 封装PUT请求
export const put = (url, data = {}) => {
  return http.put(url, data);
};

// 封装DELETE请求
export const del = (url, params = {}) => {
  return http.delete(url, { params });
};

export default http;
