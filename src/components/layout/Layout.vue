<template>
  <div class="layout-container">
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="header-content">
        <!-- Logo 和品牌名 -->
        <div class="brand">
          <img src="/logo.svg" alt="Logo" class="logo" />
          <span class="brand-name">VideoTutor</span>
          <span class="beta-tag">Beta</span>
        </div>

        <!-- 导航菜单 -->
        <nav class="nav-menu">
          <router-link to="/" class="nav-link">
            <el-icon><HomeFilled /></el-icon>
            <span>首页</span>
          </router-link>
          <router-link to="/video-gallery" class="nav-link">
            <el-icon><VideoCamera /></el-icon>
            <span>视频展示</span>
          </router-link>
          <router-link to="/course-generator" class="nav-link">
            <el-icon><Monitor /></el-icon>
            <span>课程生成器</span>
          </router-link>
        </nav>

        <!-- 右侧操作区 -->
        <div class="header-actions">
          <!-- 语言切换 -->
          <el-dropdown class="language-dropdown">
            <el-button text class="language-btn">
              <span>🌐</span>
              <span>中文</span>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>中文</el-dropdown-item>
                <el-dropdown-item>English</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <!-- 用户头像 -->
          <el-dropdown v-if="authStore.isLoggedIn" class="user-dropdown">
            <el-avatar class="user-avatar" :size="32">
              <el-icon><User /></el-icon>
            </el-avatar>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item disabled>
                  <div class="user-info">
                    <div class="username">{{ authStore.user?.username }}</div>
                    <div class="user-role">
                      {{ authStore.userRole === 'admin' ? '管理员' : '用户' }}
                    </div>
                  </div>
                </el-dropdown-item>
                <el-dropdown-item divided @click="handleLogout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <el-button
            v-else
            text
            class="login-btn"
            @click="showLoginDialog = true"
          >
            <el-icon><User /></el-icon>
            <span>登录</span>
          </el-button>
        </div>
      </div>
    </header>

    <!-- 主内容区域 -->
    <main class="main-content">
      <slot></slot>
    </main>

    <!-- 登录对话框 -->
    <LoginDialog
      v-model="showLoginDialog"
      @login-success="handleLoginSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import {
  Monitor,
  ArrowDown,
  HomeFilled,
  VideoCamera,
  User,
  SwitchButton,
} from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { useAuthStore } from '../../stores/auth';
import LoginDialog from '../LoginDialog.vue';

const authStore = useAuthStore();
const showLoginDialog = ref(false);

onMounted(() => {
  // 初始化认证状态
  authStore.initAuth();
});

const handleLoginSuccess = () => {
  ElMessage.success('欢迎回来，管理员！');
};

const handleLogout = () => {
  authStore.logout();
  ElMessage.success('已退出登录');
};
</script>

<style scoped>
.layout-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.brand {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 700;
}

.logo {
  width: 32px;
  height: 32px;
}

.brand-name {
  font-size: 20px;
  color: #1a1a1a;
  font-weight: 700;
}

.beta-tag {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 32px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  color: #4a5568;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.nav-link.router-link-active {
  color: #667eea;
  background: rgba(102, 126, 234, 0.15);
}

.nav-link.router-link-active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 1px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.language-dropdown {
  margin-right: 8px;
}

.language-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #4a5568;
  font-weight: 500;
}

.language-btn:hover {
  color: #667eea;
}

.dropdown-icon {
  font-size: 12px;
}

.user-avatar {
  background: linear-gradient(45deg, #667eea, #764ba2);
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.login-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #4a5568;
  font-weight: 500;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.login-btn:hover {
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.user-dropdown .user-info {
  text-align: left;
  min-width: 120px;
}

.user-dropdown .username {
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.user-dropdown .user-role {
  font-size: 12px;
  color: #666;
}

.main-content {
  flex: 1;
  background: #f8fafc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }

  .nav-menu {
    display: none;
  }

  .brand-name {
    font-size: 18px;
  }

  .header-actions {
    gap: 12px;
  }
}

@media (max-width: 1024px) {
  .nav-menu {
    gap: 20px;
  }

  .nav-link span {
    display: none;
  }
}
</style>
