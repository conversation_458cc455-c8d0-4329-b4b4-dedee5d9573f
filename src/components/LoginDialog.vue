<template>
  <el-dialog
    v-model="dialogVisible"
    title="用户登录"
    width="400px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    center
  >
    <el-form
      ref="loginFormRef"
      :model="loginForm"
      :rules="loginRules"
      label-width="80px"
      @submit.prevent="handleLogin"
    >
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model="loginForm.username"
          placeholder="请输入用户名"
          prefix-icon="User"
          clearable
        />
      </el-form-item>

      <el-form-item label="密码" prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          placeholder="请输入密码"
          prefix-icon="Lock"
          show-password
          clearable
        />
      </el-form-item>

      <el-form-item>
        <el-checkbox v-model="loginForm.rememberMe">记住我</el-checkbox>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleLogin">
          {{ loading ? '登录中...' : '登录' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { useAuthStore } from '../stores/auth';

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
});

// Emits
const emit = defineEmits(['update:modelValue', 'login-success']);

// Store
const authStore = useAuthStore();

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const loginFormRef = ref();
const loading = ref(false);

const loginForm = ref({
  username: '',
  password: '',
  rememberMe: false,
});

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    {
      min: 2,
      max: 20,
      message: '用户名长度在 2 到 20 个字符',
      trigger: 'blur',
    },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' },
  ],
};

// 监听对话框显示状态
watch(dialogVisible, (newVal) => {
  if (newVal) {
    // 对话框打开时重置表单
    resetForm();
  }
});

// 重置表单
const resetForm = () => {
  loginForm.value = {
    username: '',
    password: '',
    rememberMe: false,
  };
  if (loginFormRef.value) {
    loginFormRef.value.clearValidate();
  }
};

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return;

  try {
    // 验证表单
    await loginFormRef.value.validate();

    loading.value = true;

    // 调用登录API
    const result = await authStore.login({
      username: loginForm.value.username,
      password: loginForm.value.password,
    });

    if (result.success) {
      ElMessage.success('登录成功！');
      emit('login-success', result.user);
      dialogVisible.value = false;
    } else {
      ElMessage.error(result.error || '登录失败，请检查用户名和密码');
    }
  } catch (error) {
    console.error('登录错误:', error);
    ElMessage.error('登录失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false;
  resetForm();
};
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-dialog__header) {
  text-align: center;
  font-weight: bold;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
