import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const isAuthenticated = ref(false);
  const user = ref(null);
  const token = ref('');

  // 初始化认证状态
  const initAuth = () => {
    const savedToken = localStorage.getItem('auth_token');
    const savedUser = localStorage.getItem('auth_user');

    if (savedToken && savedUser) {
      token.value = savedToken;
      user.value = JSON.parse(savedUser);
      isAuthenticated.value = true;
    }
  };

  // 登录
  const login = async (credentials) => {
    try {
      // 这里应该调用实际的登录API
      // 目前使用模拟登录
      const mockUser = {
        id: 1,
        username: credentials.username,
        email: `${credentials.username}@example.com`,
        role: 'admin',
      };

      const mockToken = 'mock_jwt_token_' + Date.now();

      // 保存到本地存储
      localStorage.setItem('auth_token', mockToken);
      localStorage.setItem('auth_user', JSON.stringify(mockUser));

      // 更新状态
      token.value = mockToken;
      user.value = mockUser;
      isAuthenticated.value = true;

      return { success: true, user: mockUser };
    } catch (error) {
      console.error('登录失败:', error);
      return { success: false, error: error.message };
    }
  };

  // 登出
  const logout = () => {
    // 清除本地存储
    localStorage.removeItem('auth_token');
    localStorage.removeItem('auth_user');

    // 重置状态
    token.value = '';
    user.value = null;
    isAuthenticated.value = false;
  };

  // 检查是否已认证
  const checkAuth = () => {
    return isAuthenticated.value;
  };

  // 获取用户信息
  const getUser = () => {
    return user.value;
  };

  return {
    // 状态
    isAuthenticated,
    user,
    token,

    // 方法
    initAuth,
    login,
    logout,
    checkAuth,
    getUser,
  };
});
