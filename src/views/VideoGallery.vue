<template>
  <div class="video-gallery">
    <!-- Hero 区域 -->
    <section class="hero-section">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            探索视频世界
            <span class="gradient-text">学习无界限</span>
          </h1>
          <p class="hero-description">
            发现精彩的教育视频内容，从数学、科学到语言学习，应有尽有
          </p>
        </div>
        <div class="hero-decorations">
          <div class="decoration decoration-1"></div>
          <div class="decoration decoration-2"></div>
          <div class="decoration decoration-3"></div>
          <div class="decoration decoration-4"></div>
        </div>
      </div>
    </section>

    <!-- 搜索和筛选区域 -->
    <section class="search-section">
      <div class="search-container">
        <div class="search-box">
          <div class="search-input-wrapper">
            <el-input
              v-model="searchQuery"
              placeholder="搜索视频标题或描述..."
              size="large"
              clearable
              @keyup.enter="handleSearch"
              @clear="handleSearch"
              class="search-input"
            >
              <template #prefix>
                <el-icon class="search-icon"><Search /></el-icon>
              </template>
            </el-input>
            <el-button
              type="primary"
              size="large"
              @click="handleSearch"
              :loading="loading"
              class="search-btn"
            >
              搜索
            </el-button>
          </div>
        </div>

        <!-- 筛选选项 -->
        <div class="filter-options">
          <div class="filter-group">
            <span class="filter-label">分类:</span>
            <el-select
              v-model="selectedCategory"
              placeholder="选择分类"
              clearable
              @change="handleSearch"
              class="filter-select"
            >
              <el-option
                v-for="category in categories"
                :key="category.id"
                :label="category.name"
                :value="category.id"
              />
            </el-select>
          </div>

          <div class="filter-group">
            <span class="filter-label">排序:</span>
            <el-select
              v-model="sortBy"
              @change="handleSearch"
              class="filter-select"
            >
              <el-option label="最新上传" value="created_at" />
              <el-option label="最多播放" value="play_count" />
              <el-option label="最多点赞" value="like_count" />
            </el-select>
          </div>
        </div>
      </div>
    </section>

    <!-- 支持的主题标签 -->
    <section class="topics-section">
      <div class="topics-container">
        <h3 class="topics-title">支持的主题:</h3>
        <div class="topic-tags">
          <div class="topic-tag">
            <div class="topic-icon math-icon">📊</div>
            <div class="topic-info">
              <div class="topic-name">数学</div>
              <div class="topic-desc">代数、几何、统计学</div>
            </div>
          </div>
          <div class="topic-tag">
            <div class="topic-icon science-icon">🔬</div>
            <div class="topic-info">
              <div class="topic-name">科学</div>
              <div class="topic-desc">物理、化学、生物</div>
            </div>
          </div>
          <div class="topic-tag">
            <div class="topic-icon language-icon">🌍</div>
            <div class="topic-info">
              <div class="topic-name">语言</div>
              <div class="topic-desc">词汇、语法、口语</div>
            </div>
          </div>
          <div class="topic-tag">
            <div class="topic-icon tech-icon">💻</div>
            <div class="topic-info">
              <div class="topic-name">技术</div>
              <div class="topic-desc">编程、设计、工程</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 视频列表 -->
    <section class="videos-section">
      <div class="videos-container">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-grid">
          <div v-for="n in 8" :key="n" class="video-skeleton">
            <el-skeleton animated>
              <template #template>
                <el-skeleton-item variant="image" class="skeleton-thumbnail" />
                <div class="skeleton-content">
                  <el-skeleton-item variant="h3" class="skeleton-title" />
                  <el-skeleton-item variant="text" class="skeleton-desc" />
                  <div class="skeleton-meta">
                    <el-skeleton-item variant="text" style="width: 60px" />
                    <el-skeleton-item variant="text" style="width: 80px" />
                  </div>
                </div>
              </template>
            </el-skeleton>
          </div>
        </div>

        <!-- 视频网格 -->
        <div v-if="!loading && videos.length > 0" class="videos-grid">
          <div
            v-for="video in videos"
            :key="video.id"
            class="video-card"
            @click="handleVideoClick(video)"
          >
            <div class="video-thumbnail">
              <img
                :src="video.thumbnail || '/default-video-thumbnail.jpg'"
                :alt="video.title"
                class="thumbnail-image"
              />
              <div class="video-duration">
                {{ formatDuration(video.duration) }}
              </div>
              <div class="play-overlay">
                <div class="play-button">
                  <el-icon class="play-icon"><VideoPlay /></el-icon>
                </div>
              </div>
            </div>

            <div class="video-info">
              <h3 class="video-title">{{ video.title }}</h3>
              <p class="video-description">{{ video.description }}</p>

              <div class="video-meta">
                <div class="video-stats">
                  <span class="stat-item">
                    <el-icon><View /></el-icon>
                    {{ formatNumber(video.play_count) }}
                  </span>
                  <span class="stat-item">
                    <el-icon><Star /></el-icon>
                    {{ formatNumber(video.like_count) }}
                  </span>
                </div>
                <span class="video-date">{{
                  formatDate(video.created_at)
                }}</span>
              </div>

              <div class="uploader-info">
                <el-avatar
                  :src="video.uploader.avatar"
                  :size="24"
                  class="uploader-avatar"
                />
                <span class="uploader-name">{{ video.uploader.name }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && videos.length === 0" class="empty-state">
          <div class="empty-icon">🎬</div>
          <h3 class="empty-title">暂无视频内容</h3>
          <p class="empty-description">试试调整搜索条件或稍后再来看看</p>
        </div>
      </div>
    </section>

    <!-- 分页 -->
    <section v-if="!loading && videos.length > 0" class="pagination-section">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[12, 24, 48, 96]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="custom-pagination"
      />
    </section>

    <!-- 视频详情对话框 -->
    <el-dialog
      v-model="videoDialogVisible"
      :title="selectedVideo?.title"
      width="90%"
      :before-close="handleDialogClose"
      class="video-dialog"
    >
      <div v-if="selectedVideo" class="video-detail">
        <div class="video-player-container">
          <video
            :src="selectedVideo.video_url"
            controls
            class="video-player"
            :poster="selectedVideo.thumbnail"
          >
            您的浏览器不支持视频播放
          </video>
        </div>

        <div class="video-detail-info">
          <h2 class="detail-title">{{ selectedVideo.title }}</h2>
          <p class="detail-description">{{ selectedVideo.description }}</p>

          <div class="detail-meta">
            <span class="meta-item">
              <el-icon><Calendar /></el-icon>
              上传时间：{{ formatDate(selectedVideo.created_at) }}
            </span>
            <span class="meta-item">
              <el-icon><View /></el-icon>
              播放次数：{{ formatNumber(selectedVideo.play_count) }}
            </span>
            <span class="meta-item">
              <el-icon><Star /></el-icon>
              点赞数：{{ formatNumber(selectedVideo.like_count) }}
            </span>
            <span class="meta-item">
              <el-icon><Collection /></el-icon>
              分类：{{ selectedVideo.category?.name }}
            </span>
          </div>

          <div class="uploader-detail">
            <el-avatar
              :src="selectedVideo.uploader.avatar"
              :size="48"
              class="detail-avatar"
            />
            <div class="uploader-text">
              <div class="uploader-name-large">
                {{ selectedVideo.uploader.name }}
              </div>
              <div class="uploader-description">
                {{ selectedVideo.uploader.description }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Search,
  VideoPlay,
  View,
  Star,
  Calendar,
  Collection,
} from '@element-plus/icons-vue';
import { getVideoList, getVideoCategories } from '../api/videoApi';

// 响应式数据
const loading = ref(false);
const videos = ref([]);
const categories = ref([]);
const searchQuery = ref('');
const selectedCategory = ref('');
const sortBy = ref('created_at');
const currentPage = ref(1);
const pageSize = ref(12);
const total = ref(0);
const videoDialogVisible = ref(false);
const selectedVideo = ref(null);

// 获取视频列表
const fetchVideos = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchQuery.value,
      category_id: selectedCategory.value,
      sort_by: sortBy.value,
    };

    const response = await getVideoList(params);
    videos.value = response.data || [];
    total.value = response.total || 0;
  } catch (error) {
    console.error('获取视频列表失败:', error);
    ElMessage.error('获取视频列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取分类列表
const fetchCategories = async () => {
  try {
    const response = await getVideoCategories();
    categories.value = response.data || [];
  } catch (error) {
    console.error('获取分类列表失败:', error);
  }
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  fetchVideos();
};

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchVideos();
};

const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchVideos();
};

// 视频点击处理
const handleVideoClick = (video) => {
  selectedVideo.value = video;
  videoDialogVisible.value = true;
};

// 对话框关闭处理
const handleDialogClose = () => {
  videoDialogVisible.value = false;
  selectedVideo.value = null;
};

// 格式化工具函数
const formatDuration = (seconds) => {
  if (!seconds) return '00:00';
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds
    .toString()
    .padStart(2, '0')}`;
};

const formatNumber = (num) => {
  if (!num) return '0';
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now - date);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 1) return '昨天';
  if (diffDays < 7) return `${diffDays}天前`;
  if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`;
  if (diffDays < 365) return `${Math.floor(diffDays / 30)}个月前`;
  return `${Math.floor(diffDays / 365)}年前`;
};

// 组件挂载时获取数据
onMounted(() => {
  fetchCategories();
  fetchVideos();
});
</script>

<style scoped>
.video-gallery {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Hero 区域 */
.hero-section {
  padding: 80px 0 60px;
  position: relative;
  overflow: hidden;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  text-align: center;
  position: relative;
  z-index: 2;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  color: white;
  margin-bottom: 24px;
  line-height: 1.2;
}

.gradient-text {
  background: linear-gradient(45deg, #ffd89b 0%, #19547b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.hero-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.decoration {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.decoration-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.decoration-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.decoration-3 {
  width: 60px;
  height: 60px;
  top: 40%;
  left: 80%;
  animation-delay: 4s;
}

.decoration-4 {
  width: 100px;
  height: 100px;
  top: 10%;
  right: 30%;
  animation-delay: 1s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 搜索区域 */
.search-section {
  padding: 40px 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
}

.search-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.search-box {
  margin-bottom: 32px;
}

.search-input-wrapper {
  display: flex;
  gap: 16px;
  max-width: 600px;
  margin: 0 auto;
}

.search-input {
  flex: 1;
}

.search-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.search-input :deep(.el-input__wrapper:hover) {
  border-color: #667eea;
}

.search-input :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.2);
}

.search-btn {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
  border-radius: 12px;
  padding: 0 32px;
  font-weight: 600;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.search-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(102, 126, 234, 0.4);
}

.filter-options {
  display: flex;
  justify-content: center;
  gap: 32px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-weight: 600;
  color: #4a5568;
}

.filter-select {
  min-width: 150px;
}

.filter-select :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.filter-select :deep(.el-input__wrapper:hover) {
  border-color: #667eea;
}

/* 主题标签区域 */
.topics-section {
  padding: 40px 0;
  background: white;
}

.topics-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.topics-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 24px;
  text-align: center;
}

.topic-tags {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.topic-tag {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-radius: 16px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;
}

.topic-tag:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.topic-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: linear-gradient(45deg, #667eea, #764ba2);
}

.topic-name {
  font-weight: 700;
  color: #2d3748;
  font-size: 1.1rem;
}

.topic-desc {
  color: #718096;
  font-size: 0.9rem;
}

/* 视频区域 */
.videos-section {
  padding: 60px 0;
  background: #f8fafc;
  min-height: 500px;
}

.videos-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

.loading-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
}

.video-skeleton {
  background: white;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.skeleton-thumbnail {
  height: 180px;
  border-radius: 12px;
  margin-bottom: 16px;
}

.skeleton-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-title {
  height: 20px;
}

.skeleton-desc {
  height: 16px;
}

.skeleton-meta {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
}

.videos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
}

.video-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
}

.video-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.video-thumbnail {
  position: relative;
  overflow: hidden;
  background: #f7fafc;
}

.thumbnail-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.video-card:hover .thumbnail-image {
  transform: scale(1.05);
}

.video-duration {
  position: absolute;
  bottom: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-card:hover .play-overlay {
  opacity: 1;
}

.play-button {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.play-button:hover {
  background: white;
  transform: scale(1.1);
}

.play-icon {
  font-size: 24px;
  color: #667eea;
}

.video-info {
  padding: 20px;
}

.video-title {
  font-size: 1.1rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-description {
  color: #718096;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 0.85rem;
  color: #a0aec0;
}

.video-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.uploader-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.uploader-avatar {
  border: 2px solid #e2e8f0;
}

.uploader-name {
  font-size: 0.9rem;
  color: #4a5568;
  font-weight: 600;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 20px;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #4a5568;
  margin-bottom: 8px;
}

.empty-description {
  color: #718096;
  font-size: 1rem;
}

/* 分页 */
.pagination-section {
  padding: 40px 0;
  background: #f8fafc;
  display: flex;
  justify-content: center;
}

.custom-pagination :deep(.el-pagination) {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* 视频详情对话框 */
.video-dialog :deep(.el-dialog) {
  border-radius: 16px;
  overflow: hidden;
}

.video-detail {
  max-height: 80vh;
  overflow-y: auto;
}

.video-player-container {
  background: #000;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 24px;
}

.video-player {
  width: 100%;
  height: 400px;
  object-fit: contain;
}

.detail-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 12px;
}

.detail-description {
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 20px;
}

.detail-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  margin-bottom: 24px;
  padding: 16px;
  background: #f7fafc;
  border-radius: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #4a5568;
  font-size: 0.9rem;
}

.uploader-detail {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-radius: 12px;
}

.detail-avatar {
  border: 3px solid #667eea;
}

.uploader-name-large {
  font-size: 1.1rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 4px;
}

.uploader-description {
  color: #718096;
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-description {
    font-size: 1.1rem;
  }

  .search-input-wrapper {
    flex-direction: column;
  }

  .filter-options {
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .topic-tags {
    grid-template-columns: 1fr;
  }

  .videos-grid {
    grid-template-columns: 1fr;
  }

  .detail-meta {
    flex-direction: column;
    gap: 12px;
  }

  .video-player {
    height: 250px;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 60px 0 40px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .videos-container,
  .search-container,
  .topics-container,
  .hero-content {
    padding: 0 16px;
  }
}
</style>

padding: 0 16px;
