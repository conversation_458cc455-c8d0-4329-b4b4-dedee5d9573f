<template>
  <div class="flex h-full">
    <!-- 主内容区域 -->
    <div class="flex-1 flex flex-col">
      <!-- 步骤导航 -->
      <div class="bg-white p-4 mb-4 rounded-md shadow-sm">
        <el-steps :active="activeStep" finish-status="success" simple>
          <el-step title="生成课程素材" />
          <el-step title="生成课程脚本" />
          <el-step title="生成课程视频" />
        </el-steps>
      </div>

      <!-- 内容编辑区 -->
      <div
        class="flex-1 bg-white rounded-md shadow-sm p-6 flex flex-col items-center justify-center"
      >
        <!-- 第一步：输入主题和描述 -->
        <div v-if="activeStep === 0" class="text-center mb-8">
          <img src="/logo.svg" alt="Logo" class="w-24 h-24 mx-auto mb-4" />
          <h2 class="text-2xl font-bold mb-2">交互式视频课程生成器</h2>
          <p class="text-gray-500 mb-6">
            你需要完成一个个步骤，我们将为你生成交互式视频课程
          </p>
        </div>

        <div v-if="activeStep === 0" class="w-full max-w-2xl">
          <el-input
            v-model="topic"
            placeholder="请输入课程主题..."
            class="mb-4"
          />
          <el-input
            v-model="description"
            type="textarea"
            :rows="5"
            placeholder="请输入你的描述..."
            class="mb-4"
          />
          <div class="flex justify-center">
            <el-button type="primary" size="large" @click="generateCourse">
              <el-icon class="mr-1"><VideoPlay /></el-icon> 开始生成
            </el-button>
            <el-button type="primary" size="large" @click="mergeVideo">
              <el-icon class="mr-1"><VideoPlay /></el-icon> 合成视频测试
            </el-button>
          </div>
        </div>

        <!-- 第二步：场景大纲和故事板 -->
        <div v-if="activeStep >= 1 && activeStep < 5" class="w-full">
          <h2 class="text-2xl font-bold mb-6 text-center">课程生成流程</h2>

          <!-- 场景大纲横向排列 -->
          <div class="mb-10 overflow-x-auto">
            <div class="flex space-x-4 min-w-max px-4">
              <div
                v-for="scene in sceneOutline"
                :key="scene.sceneNum"
                class="w-64 p-4 border rounded-lg shadow-sm bg-white flex-shrink-0 relative whitespace-pre-line"
                :id="`scene-${scene.sceneNum}`"
              >
                <div class="flex justify-between items-center mb-3">
                  <h3 class="text-lg font-bold truncate">
                    场景 {{ scene.sceneNum }}
                  </h3>
                  <el-tag size="small" type="primary">{{
                    scene.sceneNum
                  }}</el-tag>
                </div>
                <div class="text-sm font-semibold mb-2">
                  场景 {{ scene.sceneNum }}
                </div>
                <div
                  class="text-xs text-gray-600 leading-relaxed whitespace-pre-line"
                >
                  {{ scene.content }}
                </div>

                <!-- 连接线的起点 -->
                <div
                  v-if="scene.storyboard"
                  class="absolute bottom-0 left-1/2 w-1 h-4 bg-blue-400 transform -translate-x-1/2"
                ></div>
              </div>
            </div>
          </div>

          <!-- 故事板展示区域 -->
          <div class="mb-10 overflow-x-auto">
            <div class="flex space-x-4 min-w-max px-4">
              <div
                v-for="scene in sceneOutline.filter((s) => s.storyboard)"
                :key="`story-${scene.sceneNum}`"
                :id="`storyboard-${scene.sceneNum}`"
                class="w-64 p-4 border rounded-lg shadow-sm bg-white flex-shrink-0 relative"
              >
                <div class="flex justify-between items-center mb-3">
                  <h3 class="text-lg font-bold truncate">
                    场景 {{ scene.sceneNum }} 故事板
                  </h3>
                  <el-tag size="small" type="primary">{{
                    scene.sceneNum
                  }}</el-tag>
                </div>
                <div class="text-xs text-gray-500">
                  {{ scene.storyboard.sceneVision }}
                </div>
                <div class="text-xs text-gray-500">
                  {{ scene.storyboard.content }}
                </div>

                <!-- 连接线的起点 -->
                <div
                  v-if="scene.storyboard"
                  class="absolute bottom-0 left-1/2 w-1 h-4 bg-blue-400 transform -translate-x-1/2"
                ></div>
              </div>
            </div>
          </div>
          <!-- 技术实现环节已移除 -->
          <!-- 动画和旁白 -->
          <div class="mb-10 overflow-x-auto">
            <div class="flex space-x-4 min-w-max px-4">
              <div
                v-for="scene in sceneOutline.filter(
                  (s) => s.animationNarration
                )"
                :key="`tl-${scene.sceneNum}`"
                :id="`tl-${scene.sceneNum}`"
                class="w-64 p-4 border rounded-lg shadow-sm bg-white flex-shrink-0 relative"
              >
                <div class="flex justify-between items-center mb-3">
                  <h3 class="text-lg font-bold truncate">
                    场景 {{ scene.sceneNum }} 动画旁白
                  </h3>
                  <el-tag size="small" type="primary">{{
                    scene.sceneNum
                  }}</el-tag>
                </div>
                <div class="text-xs text-gray-500">
                  {{ scene.animationNarration }}
                </div>

                <!-- 连接线的起点 -->
                <div
                  v-if="scene.animationNarration"
                  class="absolute bottom-0 left-1/2 w-1 h-4 bg-blue-400 transform -translate-x-1/2"
                ></div>
              </div>
            </div>
          </div>

          <!-- 底部按钮 -->
          <div v-if="activeStep === 1" class="flex justify-center mt-6">
            <el-button
              type="primary"
              @click="generateAllStoryboards"
              :loading="generatingAll"
            >
              一键生成所有故事板 <el-icon class="ml-1"><ArrowRight /></el-icon>
            </el-button>
          </div>

          <div v-if="activeStep === 2" class="flex justify-center mt-6">
            <el-button
              type="primary"
              @click="generateAnimationNarration"
              :loading="generatingAll"
            >
              一键生成动画旁白 <el-icon class="ml-1"><ArrowRight /></el-icon>
            </el-button>
          </div>
          <div v-if="activeStep === 3" class="flex justify-center mt-6">
            <el-button
              type="primary"
              @click="generateAnimationNarrationHandler"
              :loading="generatingAll"
            >
              一键生成动画和旁白 <el-icon class="ml-1"><ArrowRight /></el-icon>
            </el-button>
          </div>
          <div v-if="activeStep === 4" class="flex justify-center mt-6">
            <el-button
              type="primary"
              @click="generateVideo"
              :loading="generatingAll"
            >
              生成动画 <el-icon class="ml-1"><ArrowRight /></el-icon>
            </el-button>
          </div>
        </div>

        <!-- 第五步：视频播放 -->
        <div v-if="activeStep === 5" class="w-full max-w-4xl">
          <h2 class="text-2xl font-bold mb-6 text-center">课程视频</h2>

          <div v-if="videoUrl" class="mb-6">
            <video
              class="w-full rounded-lg shadow-lg"
              controls
              autoplay
              :src="videoUrl"
            ></video>
          </div>
          <div v-else class="text-center text-gray-500 py-10">
            <el-icon class="text-4xl mb-4"><VideoPlay /></el-icon>
            <p>视频正在处理中，请稍候...</p>
          </div>

          <div class="flex justify-center mt-6">
            <el-button
              type="primary"
              @click="downloadVideo"
              :disabled="!videoUrl"
            >
              下载视频 <el-icon class="ml-1"><Download /></el-icon>
            </el-button>
            <el-button @click="activeStep = 0" class="ml-4">
              创建新课程 <el-icon class="ml-1"><Plus /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧智能信息面板 -->
    <!-- <div class="w-80 ml-4 bg-white rounded-md shadow-sm p-4 flex flex-col">
      <h3 class="text-lg font-bold mb-4 flex items-center">
        <el-icon class="mr-2"><ChatDotRound /></el-icon> 智能助手
      </h3>

      <div class="flex-1 overflow-auto mb-4 text-sm">
        <div v-if="!generationStarted" class="text-gray-500">
          <p class="mb-2">👋 你好！我是你的AI助手。</p>
          <p class="mb-2">我可以帮助你生成交互式视频课程。</p>
          <p>
            请在左侧输入框中描述你想要的课程主题和要求，然后点击「开始生成」按钮。
          </p>
        </div>
        <div v-else>
          <div class="mb-4 p-3 bg-gray-100 rounded-md">
            <div class="font-bold mb-1">你的输入：</div>
            <div class="text-gray-700">{{ description }}</div>
          </div>

          <div class="mb-2 font-bold">生成进度：</div>
          <div class="mb-4">
            <el-progress :percentage="generationProgress" />
          </div>

          <div class="p-3 bg-blue-50 rounded-md mb-3">
            <div class="font-bold mb-1 text-blue-800">AI反馈：</div>
            <div class="text-blue-700">
              正在分析你的需求，生成适合的课程内容...
            </div>
          </div>
        </div>
      </div>

      <div class="border-t pt-4">
        <div class="text-sm text-gray-500 mb-2">生成设置</div>
        <div class="mb-3">
          <div class="flex justify-between mb-1">
            <span class="text-sm">创意程度</span>
            <span class="text-sm text-gray-500">{{ creativityLevel }}</span>
          </div>
          <el-slider v-model="creativityLevel" :min="1" :max="10" />
        </div>
        <div>
          <div class="flex justify-between mb-1">
            <span class="text-sm">生成速度</span>
            <span class="text-sm text-gray-500">{{
              generationSpeed === 1 ? "慢" : generationSpeed === 2 ? "中" : "快"
            }}</span>
          </div>
          <el-slider v-model="generationSpeed" :min="1" :max="3" :step="1" />
        </div>
      </div>
    </div> -->
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue';
import {
  VideoPlay,
  ChatDotRound,
  ArrowRight,
  Connection,
} from '@element-plus/icons-vue';
import { videoGeneratorApi } from '../api/videoGeneratorApi';
import { ElMessage } from 'element-plus';
// // 生成技术实现
// generateTechnicalPlan: (data) => {
//   return post("/generateTechnicalImplementation", data);
// },
// // 生成动画旁白
// generateAnimationNarration: (data) => {
//   return post("/generateAnimationNarration", data);
// },
// 步骤状态
const activeStep = ref(0);

// 用户输入
// const topic = ref('等腰三角形的顶角平分线或底边的中线垂直于底边');
// const description = ref(
//   '使用动画演示证明一下等腰三角形的顶角平分线或底边的中线垂直于底边，需要公式推导'
// );
const topic = ref('试题讲解');
const description = ref(
  '两支粗细、长短都不同的蜡烛，长的一支可以点4小时, 短的可以点6小时, 将它们同时点燃, 两小时后, 两支蜡烛所余下的长度正好相等．原来短蜡烛的长度是长蜡烛的多少？'
);
const sceneOutline = ref([]);
const sessionId = ref('');
const generatingAll = ref(false);

// 开始生成课程
const generateCourse = async () => {
  if (!topic.value.trim()) {
    ElMessage.warning('请先输入主题');
    return;
  }

  if (!description.value.trim()) {
    // 提示用户输入内容
    ElMessage.warning('请先输入描述和要求');
    return;
  }

  const res = await videoGeneratorApi.generateSceneOutline({
    topic: topic.value,
    description: description.value,
  });
  if (res.code !== 0) {
    ElMessage.error('生成场景大纲失败');
    return;
  }
  sessionId.value = res.data.sessionId;
  // 为每个场景添加状态属性
  sceneOutline.value = res.data.sceneList.map((scene) => ({
    ...scene,
    storyboard: null,
    loading: false,
    implLoading: false,
  }));
  console.log('解析后的场景大纲:', sceneOutline.value);
  activeStep.value = 1;
};

// 生成所有故事板
const generateAllStoryboards = async () => {
  generatingAll.value = true;

  try {
    const promises = sceneOutline.value
      .filter((scene) => !scene.storyboard)
      .map((scene) => {
        scene.loading = true;
        return videoGeneratorApi
          .generateStoryboard({
            topic: topic.value,
            description: description.value,
            sessionId: sessionId.value,
            sceneNum: scene.sceneNum,
          })
          .then((res) => {
            if (res.code === 0) {
              scene.storyboard = res.data.storyboard;
            }
            scene.loading = false;
            return res;
          });
      });

    await Promise.all(promises);
    ElMessage.success('所有故事板生成完成');
    activeStep.value = 2;
  } catch (error) {
    console.error('生成所有故事板出错:', error);
    ElMessage.error('生成故事板过程中出现错误');
  } finally {
    generatingAll.value = false;
  }
};

// 技术实现生成函数已移除

// 生成动画旁白（步骤2使用）
const generateAnimationNarration = async () => {
  generatingAll.value = true;

  try {
    const promises = sceneOutline.value
      .filter((scene) => !scene.animationNarration)
      .map((scene) => {
        scene.loading = true;
        return videoGeneratorApi
          .generateAnimationNarration({
            topic: topic.value,
            description: description.value,
            sessionId: sessionId.value,
            sceneNum: scene.sceneNum,
          })
          .then((res) => {
            if (res.code === 0) {
              scene.animationNarration = res.data.animationNarration;
            }
            scene.loading = false;
            return res;
          });
      });

    await Promise.all(promises);
    ElMessage.success('所有动画旁白生成完成');
    activeStep.value = 3;
  } catch (error) {
    console.error('生成动画旁白出错:', error);
    ElMessage.error('生成动画旁白过程中出现错误');
  } finally {
    generatingAll.value = false;
  }
};

const generateAnimationNarrationHandler = async () => {
  generatingAll.value = true;

  try {
    const promises = sceneOutline.value
      .filter((scene) => !scene.animationNarration)
      .map((scene) => {
        scene.loading = true;
        return videoGeneratorApi
          .generateAnimationNarration({
            topic: topic.value,
            description: description.value,
            sessionId: sessionId.value,
            sceneNum: scene.sceneNum,
          })
          .then((res) => {
            if (res.code === 0) {
              scene.animationNarration = res.data.animationNarration;
            }
            scene.loading = false;
            return res;
          });
      });

    await Promise.all(promises);
    ElMessage.success('所有动画和旁白生成完成');
    activeStep.value = 4;
  } catch (error) {
    console.error('生成所有动画和旁白出错:', error);
    ElMessage.error('生成动画和旁白过程中出现错误');
  } finally {
    generatingAll.value = false;
  }
};
const generateVideo = async () => {
  generatingAll.value = true;

  try {
    const promises = sceneOutline.value.map((scene) => {
      scene.loading = true;
      return videoGeneratorApi
        .generateVideo({
          topic: topic.value,
          description: description.value,
          sessionId: sessionId.value,
          sceneNum: scene.sceneNum,
        })
        .then((res) => {
          if (res.code === 0) {
          }
          scene.loading = false;
          return res;
        });
    });

    await Promise.all(promises);
    ElMessage.success('所有动画生成完成');
    await mergeVideo();
    activeStep.value = 5;
  } catch (error) {
    console.error('生成所有动画出错:', error);
    ElMessage.error('生成动画过程中出现错误');
  } finally {
    generatingAll.value = false;
  }
};
const videoUrl = ref('');
const mergeVideo = async () => {
  console.log(
    '开始合并视频',
    sessionId.value || 'f74f5c75-1727-4956-8a13-b2a9c818f5e5'
  );
  const res = await videoGeneratorApi.generateVideoMerge({
    sessionId: sessionId.value || 'f74f5c75-1727-4956-8a13-b2a9c818f5e5',
  });
  if (res.code === 0) {
    videoUrl.value = res.data.videoUrl;
  } else {
    ElMessage.error('视频合并失败');
  }
};
</script>
