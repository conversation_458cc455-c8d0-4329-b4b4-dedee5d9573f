<template>
  <div class="model-training-container">
    <div class="bg-white p-4 mb-4 rounded-md shadow-sm">
      <h2 class="text-xl font-bold">模型训练</h2>
      <p class="text-gray-500">训练和优化AI模型，提高生成内容的质量和相关性</p>
    </div>

    <div class="bg-white rounded-md shadow-sm p-6">
      <el-tabs type="border-card">
        <el-tab-pane label="模型列表">
          <div class="mb-4 flex justify-between items-center">
            <el-input
              placeholder="搜索模型"
              prefix-icon="Search"
              class="w-60"
            />
            <el-button type="primary">
              <el-icon class="mr-1"><Plus /></el-icon> 新建模型
            </el-button>
          </div>

          <el-table :data="modelList" style="width: 100%">
            <el-table-column prop="name" label="模型名称" />
            <el-table-column prop="type" label="模型类型" />
            <el-table-column prop="status" label="状态">
              <template #default="scope">
                <el-tag
                  :type="
                    scope.row.status === '训练中'
                      ? 'warning'
                      : scope.row.status === '已完成'
                      ? 'success'
                      : 'info'
                  "
                >
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="progress" label="训练进度">
              <template #default="scope">
                <el-progress
                  :percentage="scope.row.progress"
                  :status="
                    scope.row.status === '已完成' ? 'success' : undefined
                  "
                />
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" />
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button
                  size="small"
                  :disabled="scope.row.status === '训练中'"
                >
                  查看
                </el-button>
                <el-button
                  size="small"
                  type="primary"
                  :disabled="scope.row.status === '训练中'"
                >
                  使用
                </el-button>
                <el-button
                  size="small"
                  type="danger"
                  :disabled="scope.row.status === '训练中'"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="mt-4 flex justify-center">
            <el-pagination
              layout="prev, pager, next"
              :total="50"
              :page-size="10"
            />
          </div>
        </el-tab-pane>

        <el-tab-pane label="训练配置">
          <div class="max-w-3xl mx-auto">
            <el-form label-position="top">
              <el-form-item label="训练数据源">
                <el-select class="w-full" placeholder="选择数据源">
                  <el-option label="本地文件上传" value="local" />
                  <el-option label="数据库导入" value="database" />
                  <el-option label="API接入" value="api" />
                </el-select>
              </el-form-item>

              <el-form-item label="模型类型">
                <el-radio-group>
                  <el-radio label="文本生成模型">文本生成模型</el-radio>
                  <el-radio label="图像识别模型">图像识别模型</el-radio>
                  <el-radio label="语音合成模型">语音合成模型</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="训练参数">
                <el-input
                  type="textarea"
                  :rows="5"
                  placeholder="请输入训练参数配置（JSON格式）"
                />
              </el-form-item>

              <el-form-item>
                <el-button type="primary">开始训练</el-button>
                <el-button>重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { Search, Plus } from "@element-plus/icons-vue";

// 模型列表数据
const modelList = ref([
  {
    name: "通用文本生成模型 v1.0",
    type: "文本生成",
    status: "已完成",
    progress: 100,
    createTime: "2023-10-15 14:30",
  },
  {
    name: "课程内容生成模型 v2.1",
    type: "文本生成",
    status: "训练中",
    progress: 65,
    createTime: "2023-10-18 09:15",
  },
  {
    name: "图像识别模型 v1.5",
    type: "图像识别",
    status: "已完成",
    progress: 100,
    createTime: "2023-10-10 11:20",
  },
  {
    name: "语音合成模型 v1.0",
    type: "语音合成",
    status: "未开始",
    progress: 0,
    createTime: "2023-10-19 16:45",
  },
]);
</script>
