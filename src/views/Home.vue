<template>
  <div class="home">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            AI驱动的
            <span class="gradient-text">学习革命</span>
          </h1>
          <p class="hero-subtitle">
            清晰的AI解释，涵盖数学、科学和语言学习——完美适用于各种考试和日常学习
          </p>
          <div class="hero-actions">
            <el-button
              type="primary"
              size="large"
              class="cta-button"
              @click="$router.push('/video-gallery')"
            >
              开始探索视频
            </el-button>
            <el-button
              size="large"
              class="secondary-button"
              @click="$router.push('/course-generator')"
            >
              创建课程
            </el-button>
          </div>
        </div>

        <!-- 装饰元素 -->
        <div class="hero-decorations">
          <div class="decoration decoration-1"></div>
          <div class="decoration decoration-2"></div>
          <div class="decoration decoration-3"></div>
          <div class="decoration decoration-4"></div>
        </div>
      </div>
    </section>

    <!-- 特色功能区域 -->
    <section class="features-section">
      <div class="features-container">
        <h2 class="section-title">强大的学习工具</h2>
        <p class="section-subtitle">为每个学习者量身定制的AI教育解决方案</p>

        <div class="features-grid">
          <div class="feature-card" @click="$router.push('/video-gallery')">
            <div class="feature-icon video-icon">
              <el-icon><VideoCamera /></el-icon>
            </div>
            <h3 class="feature-title">视频展示</h3>
            <p class="feature-description">
              浏览精彩的教育视频内容，从数学、科学到语言学习，发现优质学习资源
            </p>
            <div class="feature-tags">
              <span class="tag">视频库</span>
              <span class="tag">多分类</span>
              <span class="tag">智能搜索</span>
            </div>
          </div>

          <div class="feature-card" @click="$router.push('/course-generator')">
            <div class="feature-icon course-icon">
              <el-icon><Monitor /></el-icon>
            </div>
            <h3 class="feature-title">交互式课程生成器</h3>
            <p class="feature-description">
              使用AI技术生成高质量的交互式视频课程，提升学习体验和教学效果
            </p>
            <div class="feature-tags">
              <span class="tag">AI生成</span>
              <span class="tag">交互式</span>
              <span class="tag">个性化</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 支持的学科区域 -->
    <section class="subjects-section">
      <div class="subjects-container">
        <h2 class="section-title">支持的学科</h2>
        <div class="subjects-grid">
          <div class="subject-card math-card">
            <div class="subject-icon">📊</div>
            <h3 class="subject-title">数学</h3>
            <p class="subject-desc">代数、几何、统计学和微积分</p>
            <div class="subject-features">
              <span>• 考试准备和策略</span>
              <span>• 实践练习</span>
            </div>
          </div>

          <div class="subject-card science-card">
            <div class="subject-icon">🔬</div>
            <h3 class="subject-title">科学</h3>
            <p class="subject-desc">日常科学和好奇的事实</p>
            <div class="subject-features">
              <span>• 物理、化学、生物</span>
              <span>• 实验和理论</span>
            </div>
          </div>

          <div class="subject-card language-card">
            <div class="subject-icon">🌍</div>
            <h3 class="subject-title">语言</h3>
            <p class="subject-desc">词汇、语法和口语技能</p>
            <div class="subject-features">
              <span>• 多语言支持</span>
              <span>• 交流技巧</span>
            </div>
          </div>

          <div class="subject-card tech-card">
            <div class="subject-icon">💻</div>
            <h3 class="subject-title">技术</h3>
            <p class="subject-desc">编程、设计和工程技术</p>
            <div class="subject-features">
              <span>• 前沿技术</span>
              <span>• 实战项目</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 统计数据区域 -->
    <section class="stats-section">
      <div class="stats-container">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">10K+</div>
            <div class="stat-label">学习视频</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">50K+</div>
            <div class="stat-label">活跃用户</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">95%</div>
            <div class="stat-label">满意度</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">24/7</div>
            <div class="stat-label">AI支持</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 行动号召区域 -->
    <section class="cta-section">
      <div class="cta-container">
        <div class="cta-content">
          <h2 class="cta-title">准备好开始学习了吗？</h2>
          <p class="cta-description">
            加入我们的学习社区，体验AI驱动的个性化教育
          </p>
          <el-button
            type="primary"
            size="large"
            class="cta-button-large"
            @click="$router.push('/video-gallery')"
          >
            立即开始学习
          </el-button>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { Monitor, VideoCamera } from '@element-plus/icons-vue';
</script>

<style scoped>
.home {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 英雄区域 */
.hero-section {
  padding: 100px 0 80px;
  position: relative;
  overflow: hidden;
  min-height: 70vh;
  display: flex;
  align-items: center;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  text-align: center;
  position: relative;
  z-index: 2;
}

.hero-title {
  font-size: 4rem;
  font-weight: 900;
  color: white;
  margin-bottom: 24px;
  line-height: 1.1;
}

.gradient-text {
  background: linear-gradient(45deg, #ffd89b 0%, #19547b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  max-width: 700px;
  margin: 0 auto 40px;
  line-height: 1.6;
}

.hero-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-button {
  background: linear-gradient(45deg, #ffd89b, #19547b);
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-weight: 700;
  font-size: 1.1rem;
  box-shadow: 0 8px 32px rgba(255, 216, 155, 0.4);
  transition: all 0.3s ease;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(255, 216, 155, 0.6);
}

.secondary-button {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  border-radius: 12px;
  padding: 16px 32px;
  font-weight: 600;
  font-size: 1.1rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.secondary-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.hero-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.decoration {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 8s ease-in-out infinite;
}

.decoration-1 {
  width: 100px;
  height: 100px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.decoration-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.decoration-3 {
  width: 80px;
  height: 80px;
  top: 40%;
  left: 80%;
  animation-delay: 4s;
}

.decoration-4 {
  width: 120px;
  height: 120px;
  top: 10%;
  right: 30%;
  animation-delay: 1s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-30px) rotate(180deg);
  }
}

/* 特色功能区域 */
.features-section {
  padding: 80px 0;
  background: white;
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #2d3748;
  text-align: center;
  margin-bottom: 16px;
}

.section-subtitle {
  font-size: 1.2rem;
  color: #718096;
  text-align: center;
  margin-bottom: 60px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
}

.feature-card {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-radius: 20px;
  padding: 32px;
  text-align: center;
  border: 2px solid transparent;
  transition: all 0.4s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.feature-card:hover::before {
  opacity: 0.05;
}

.feature-card:hover {
  transform: translateY(-8px);
  border-color: #667eea;
  box-shadow: 0 20px 60px rgba(102, 126, 234, 0.2);
}

.feature-card > * {
  position: relative;
  z-index: 2;
}

.feature-icon {
  width: 80px;
  height: 80px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  font-size: 2rem;
  color: white;
  transition: all 0.3s ease;
}

.video-icon {
  background: linear-gradient(45deg, #667eea, #764ba2);
}

.course-icon {
  background: linear-gradient(45deg, #f093fb, #f5576c);
}

.feature-card:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 16px;
}

.feature-description {
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 20px;
}

.feature-tags {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.tag {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.85rem;
  font-weight: 600;
}

/* 支持的学科区域 */
.subjects-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.subjects-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.subjects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.subject-card {
  background: white;
  border-radius: 16px;
  padding: 28px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.subject-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.math-card:hover {
  border-color: #f093fb;
}

.science-card:hover {
  border-color: #4facfe;
}

.language-card:hover {
  border-color: #43e97b;
}

.tech-card:hover {
  border-color: #fa709a;
}

.subject-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.subject-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
}

.subject-desc {
  color: #4a5568;
  margin-bottom: 16px;
  line-height: 1.5;
}

.subject-features {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 0.9rem;
  color: #718096;
}

/* 统计数据区域 */
.stats-section {
  padding: 60px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
}

.stat-item {
  text-align: center;
  color: white;
}

.stat-number {
  font-size: 3rem;
  font-weight: 900;
  margin-bottom: 8px;
  background: linear-gradient(45deg, #ffd89b, #19547b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 1.1rem;
  font-weight: 600;
  opacity: 0.9;
}

/* 行动号召区域 */
.cta-section {
  padding: 80px 0;
  background: white;
}

.cta-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 24px;
}

.cta-content {
  text-align: center;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-radius: 20px;
  padding: 60px 40px;
  border: 2px solid #e2e8f0;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #2d3748;
  margin-bottom: 16px;
}

.cta-description {
  font-size: 1.2rem;
  color: #4a5568;
  margin-bottom: 32px;
  line-height: 1.6;
}

.cta-button-large {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
  border-radius: 12px;
  padding: 20px 40px;
  font-weight: 700;
  font-size: 1.2rem;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.cta-button-large:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .cta-button,
  .secondary-button {
    width: 100%;
    max-width: 300px;
  }

  .section-title {
    font-size: 2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .subjects-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .cta-title {
    font-size: 2rem;
  }

  .cta-content {
    padding: 40px 24px;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 80px 0 60px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .features-section,
  .subjects-section,
  .cta-section {
    padding: 60px 0;
  }

  .stats-section {
    padding: 40px 0;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .hero-content,
  .features-container,
  .subjects-container,
  .stats-container,
  .cta-container {
    padding: 0 16px;
  }
}
</style>
