{"name": "vue-admin-layout", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@iconify-json/ep": "^1.2.2", "@unocss/preset-icons": "^66.1.0-beta.10", "@unocss/preset-uno": "^66.1.0-beta.10", "@unocss/preset-wind3": "^66.1.0-beta.10", "@unocss/vite": "^66.1.0-beta.10", "axios": "^1.8.4", "element-plus": "^2.9.7", "pinia": "^3.0.3", "unocss": "^66.1.0-beta.10", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "vite": "^6.2.0"}}